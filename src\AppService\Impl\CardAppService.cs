﻿using AppService.Dtos.card;
using Domain.card.Repository;
using Domain.Models.card;
using Infrastructure.Repositories;
using Infrastructure.UnitOfWorks.SqlServer;

namespace AppService.Impl
{

    /// <summary>
    /// 版 本 BPM敏捷开发框架    
    /// 创建人：Aarons
    /// 日 期：2022.03.18
    /// 描 述：储值卡应用服务
    /// </summary>
    public class CardAppService : AppServiceBase, ICardAppService
    {

        /// <summary>
        /// 订单仓储接口
        /// </summary>
        private readonly ICardRepository _cardRepository;

        /// <summary>
        /// Sql查询对象
        /// </summary>
        private ISqlQuery _sqlQuery { get; set; }

        /// <summary>
        /// 初始化应用服务
        /// </summary>
        /// <param name="orderRepository">订单仓储</param>
        public CardAppService(ICardRepository cardRepository, ISqlQuery sqlQuery, CardUnitOfWork unitOfWork)
        {
            _cardRepository = cardRepository;
            _sqlQuery = sqlQuery;
            _sqlQuery.SetConnection(unitOfWork.GetConnection());
        }


        /// <summary>
        /// 获取储值卡面值列表
        /// </summary>
        /// <param name="shop_id">门店编号</param>
        /// <returns></returns>
        public async Task<List<cardFaceDto>> getCardFaceList(string shop_id)
        {
            return await _sqlQuery.From<cardFaceEntity>().Where<cardFaceEntity>(x => x.WAREHOUSE.Equals(shop_id)).ToListAsync<cardFaceDto>();
        }


        /// <summary>
        /// 获取储值卡信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<cardDto> getCardInfo(cardQueryRequest request)
        {
            // 添加空值检查，防止空引用异常
            if (request?.card_code == null)
                return null;

            return await _sqlQuery.From<cardEntity>().Where<cardDto>(x => x.CODE, request.card_code.Trim()).ToAsync<cardDto>();
            // 这个是底层查询
            //var entity = await _cardRepository.SingleAsync(x => x.Id.Equals(request.card_code.Trim()));
            //return entity.MapTo<cardDto>();
        }

        /// <summary>
        /// 获取储值卡制卡信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<cardVFDto> getCardVFInfo(cardvfRequest request)
        {
            //var entity = await _cardRepository.getCardVfInfo(request.card_code);
            //return entity.MapTo<cardVFDto>();
            return await _sqlQuery.From<cardVFEntity>()
                .Where<cardVFEntity>(x => x.CODE, request.card_code.Trim())
                .Where<cardVFEntity>(x => x.VFCODE, request.vf_code.Trim())

                .ToAsync<cardVFDto>();
        }


        /// <summary>
        /// 保存储值卡交易记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task saveCardTxns(cardTxnsRequest request)
        {
            var card = await _cardRepository.SingleAsync(x => x.Id.Equals(request.card_code.Trim()));
            Log.Info("储值卡当前余额--->" + card.ToJson());
            var txns = request.MapTo<cardTxnsEntity>();
            txns.TXNDATE = DateTime.Now;
            txns.ORGAMT = card.BALANCE;
            card.BALANCE = card.BALANCE - txns.DEBAMT;
            card.LSTDATE = DateTime.Now;
            await _cardRepository.savePayTradeLog(card, txns);

        }


        /// <summary>
        /// 储值卡充值
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vf_code"></param>
        /// <param name="vf_paas_word"></param>
        /// <returns></returns>
        public async Task cardRecharge(cardRechargeRequest request, string vf_code, string vf_paas_word)
        {
            var card_info = request.MapTo<cardEntity>();
            card_info.Init(request.card_code, vf_code, vf_paas_word);
            var adm_txns_info = request.MapTo<cardAdmTxnsEntity>();
            adm_txns_info.Init();
            await _cardRepository.saveCard(card_info, adm_txns_info);
        }


    }
}
