﻿using AppService.Dtos.member;
using Domain.Models.member;
using System;
using System.Threading.Tasks;

namespace AppService.Impl
{
    /// <summary>
    /// Author：Aarons
    /// Create date：2022-07-05
    /// Description：会员应用服务
    /// </summary>
    public class MemberAppService : AppServiceBase, IMemberAppService
    {
        /// <summary>
        /// Sql查询对象
        /// </summary>
        protected ISqlQuery sqlQuery { get; set; }
        /// <summary>
        /// 初始化应用服务
        /// </summary>
        public MemberAppService(ISqlQuery _sqlQuery)
        {
            sqlQuery = _sqlQuery;
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  会员列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<memberDto>> getPageMemberList(memberQuery query)
        {
            var dateTime = DateTime.Now.Date;
            return await sqlQuery.From("VW_MEMBERINFO").Where<memberDto>(x => x.CREDATE < dateTime)
               .ToPagerListAsync<memberDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        public async Task<PagerList<memberDtoList>> getPageMemberListNew(memberQuery query)
        {
            var dateTime = DateTime.Now.Date;
            return await sqlQuery.From("VW_MEMBERINFO")
               .ToPagerListAsync<memberDtoList>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-09
        /// Description:  会员信息
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<List<memberDto>> getMemberInfo(memberRequest request)
        {
            var member = await sqlQuery.From("VW_MEMBERINFO").WhereIfNotEmpty<memberDto>(x => x.PHONE, request.code).ToListAsync<memberDto>();
            if (!member.IsNull() && member.Count > 0)
                return member;
            else
                return await sqlQuery.From("VW_MEMBERINFO").WhereIfNotEmpty<memberDto>(x => x.CODE, request.code).ToListAsync<memberDto>();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  会员等级信息
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        public async Task<PagerList<memberGradeDto>> getPageMemberGradeList(memberQuery query)
        {
            return await sqlQuery.From<memberGradeEntity>("a").ToPagerListAsync<memberGradeDto>(new Pager(query.Page, query.PageSize, query.Order));
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2022-07-05
        /// Description:  灵智会员等级信息
        /// </summary>
        public async Task<List<memberLzGradeDto>> getMemberYzGradeList()
        {
            return await sqlQuery.From<memberLzGradeEntity>("a").ToListAsync<memberLzGradeDto>();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-11-25-02
        /// Description:  获取客户权益
        /// </summary>
        /// <param name="request">查询条件</param>
        /// <returns></returns>
        public async Task<customerEquityDto> getCustomerEquity(string member_card_no)
        {
            return await sqlQuery.From<customerEquityEntity>("a").Where<customerEquityEntity>(x => x.member_card_no, member_card_no)
                .ToAsync<customerEquityDto>();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-11-25-02
        /// Description:  获取客户
        /// </summary>
        /// <param name="phone">查询条件</param>
        /// <returns></returns>
        public async Task<customerDto> getCustomerInfo(string phone)
        {
            // 原有代码：使用ToListAsync但只需要第一条记录，性能不佳
            //var data = await sqlQuery.From<customerEntity>("a").Where<customerEntity>(x => x.phone, phone)
            //    .ToListAsync<customerEntity>();
            //if (data != null && data.Count > 0)
            //    return data[0].MapTo<customerDto>();
            //return new customerDto();

            // 优化后：使用ToAsync，只查询第一条记录，提升性能
            var data = await sqlQuery.From<customerEntity>("a").Where<customerEntity>(x => x.phone, phone)
                .ToAsync<customerEntity>();
            if (data != null)
                return data.MapTo<customerDto>();
            return new customerDto();
        }

        /// <summary>
        /// Author:		  Aarons
        /// Create date:  2024-03-19
        /// Description:  获取会员黑名单信息
        /// </summary>
        /// <param name="code">会员号码或手机号</param>
        /// <returns></returns>
        public async Task<memberBlacklistDto> GetMemberBlacklist(string code)
        {
            // 先查询手机号
            var result = await sqlQuery.From<memberBlacklistEntity>("a")
                .Where<memberBlacklistEntity>(x => x.phone == code && x.status != 0)
                .ToAsync<memberBlacklistDto>();

            if (result == null)
            {
                // 如果没找到，则查询会员号码
                result = await sqlQuery.From<memberBlacklistEntity>("a")
                    .Where<memberBlacklistEntity>(x => x.CUSTOMER == code && x.status != 0)
                    .ToAsync<memberBlacklistDto>();
            }

            return result;
        }
    }
}
