﻿using BPM.AspNetCore.Mvc;
using BPM.Extras.Card.Request;
using BPM.Extras.Card.Result;

namespace BPM.Extras.Card.Services;

/// <summary>
/// 有赞服务
/// </summary>
public interface ICardService
{
    /// <summary>
    /// 有赞token.
    /// </summary>
    /// <returns></returns>
    Task<ApiResult> GetTokenAsync();

    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    Task<string> getCardData(string url, Dictionary<string, object> dict);


    /// <summary>
    /// 获取储值卡数据
    /// </summary>
    /// <param name="url">请求地址</param>
    /// <param name="dict">请求参数</param>
    /// <returns></returns>
    Task<CardResult<object>> getCardData(CardParameter parameter);
}