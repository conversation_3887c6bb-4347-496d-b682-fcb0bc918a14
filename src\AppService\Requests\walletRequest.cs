﻿using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace AppService.Requests;


/// <summary>
/// 电子钱包基类
/// </summary>
public class walletBase : signRequestBase
{
    /// <summary>
    ///  操作人
    /// </summary>
    [Required(ErrorMessage = "操作人[operator]不能为空")]
    public string @operator { get; set; }
    /// <summary>
    ///  mac
    /// </summary>
    [Required(ErrorMessage = "[mac]不能为空")]
    public string mac { get; set; }    

    /// <summary>
    ///  门店编号
    /// </summary>
    [Required(ErrorMessage = "仓号[shopCode]不能为空")]
    public string shopCode { get; set; }

}

/// <summary>
/// 电子钱包交易查询请求
/// </summary>
public class walletTradeQueryRequest: walletBase
{
    /// <summary>
    ///  交易类型(1=交易，2=退款)
    /// </summary>
    [Required(ErrorMessage = "[tradeType]不能为空")]
    public int tradeType { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一
    /// </summary>
    public string outTradeNo { get; set; }

    /// <summary>
    /// 商户退款交易单号，商户内保证唯一
    /// </summary>
    public string outRefundNo { get; set; }
}

/// <summary>
/// 电子钱包消费请求
/// </summary>
public class walletTradeRequest : walletBase
{

    /// <summary>
    /// 扫描二维码获取到的内容
    /// </summary>
    [Required(ErrorMessage = "二维码[payCode]不能为空")]
    public string payCode { get; set; }

    /// <summary>
    ///  1-查询会员 2-钱包支付 默认为1
    /// </summary>
    [Required(ErrorMessage = "操作类型[operateType]不能为空")]
    public int operateType { get; set; }

    /// <summary>
    /// 交易金额，单位分 operateType 为2时必传
    /// </summary>
    [Required(ErrorMessage = "交易金额[amount]不能为空")]
    public int amount { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "交易单号[outTradeNo]不能为空")]
    public string outTradeNo { get; set; }

}

/// <summary>
/// 电子钱包退款请求
/// </summary>
public class walletRefundRequest : walletBase
{
    /// <summary>
    /// 交易金额，单位分 operateType 为2时必传
    /// </summary>
    [Required(ErrorMessage = "交易金额[amount]不能为空")]
    public int amount { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "交易单号[outTradeNo]不能为空")]
    public string outTradeNo { get; set; }

    /// <summary>
    /// 商户交易单号，商户内保证唯一 ，operateType 为2 时必传
    /// </summary>
    [Required(ErrorMessage = "退款交易单号[outRefundNo]不能为空")]
    public string outRefundNo { get; set; }

}

/// <summary>
/// 手机开卡请求
/// </summary>
public class walletSendCardRequest : walletBase
{
    /// <summary>
    /// 发卡流水号
    /// </summary>
    [Required(ErrorMessage = "发卡流水号[serialNo]不能为空")]
    public string serialNo { get; set; }

    /// <summary>
    /// 店号
    /// </summary>
    [Required(ErrorMessage = "店号[outShopCode]不能为空")]
    public string outShopCode { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [Required(ErrorMessage = "手机号[phone]不能为空")]
    public string phone { get; set; }

    /// <summary>
    /// 发卡列表
    /// </summary>
    [Required(ErrorMessage = "发卡列表[sendCardList]不能为空")]
    public List<SendCardItem> sendCardList { get; set; }
}

/// <summary>
/// 发卡项目
/// </summary>
public class SendCardItem
{
    /// <summary>
    /// 流水号
    /// </summary>
    [Required(ErrorMessage = "流水号[serialNumber]不能为空")]
    public int serialNumber { get; set; }

    /// <summary>
    /// 面额
    /// </summary>
    [Required(ErrorMessage = "面额[amount]不能为空")]
    public int amount { get; set; }

    /// <summary>
    /// 付款金额
    /// </summary>
    [Required(ErrorMessage = "付款金额[payAmount]不能为空")]
    public int payAmount { get; set; }
}
