using Api.Filter;
using AppService.Requests;
using BPM.Extras.Card.Options;
using BPM.Extras.Card.Request;
using BPM.Extras.Card.Services;
using BPM.Extras.Youzan.Request;
using BPM.Extras.Youzan.Responses;
using BPM.Extras.Youzan.Result;
using BPM.Extras.Youzan.Services;
using BPM.Logs.Contents;
using BPM.Text;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
/// <summary>
///  电子钱包控制器
/// </summary>
[Route("api/wallet")]
[Authorize]
public class walletController : ApiControllerBase
{

    /// <summary>
    /// 储值卡第三方服务
    /// </summary>
    private readonly ICardService _cardService;

    /// <summary>
    ///储值卡配置.
    /// </summary>
    private readonly CardOptions _cardOptions;

    /// <summary>
    /// 有赞服务接口
    /// </summary>
    private readonly IYouzanService _youzanService;

    /// <summary>
    /// 初始化服务
    /// </summary>
    /// <param name="prodcutAppService">应用服务</param>
    public walletController(ICardService cardService
        , IOptions<CardOptions> cardOptions
        , IYouzanService youzanService)
    {
        _cardService = cardService;
        _cardOptions = cardOptions.Value ?? throw new ArgumentNullException(nameof(cardOptions));
        _youzanService = youzanService;
    }

    /// <summary>
    /// 电子钱包消费
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("trade")]
    public async Task<IActionResult> walletTrade([FromBody] walletTradeRequest request)
    {
        var memerer_info = await getMemberPhoneByCode(request.payCode);
        if (!memerer_info.success)
            return Fail(memerer_info.message);
        var customer_decode = memerer_info.data.ToString().ToObject<customerDecodeResponse>();
        var parameter = new CardParameter();
        var dict = new Dictionary<string, object>();
        dict.Add("outTradeNo", request.outTradeNo);
        dict.Add("tradeAmount", request.amount);
        dict.Add("shopCode", request.shopCode);
        dict.Add("phone", customer_decode.mobile);
        parameter.url = $"{_cardOptions.service_url}/youy/card/trade/sale";
        parameter.body = dict;
        var response = await _cardService.getCardData(parameter);
        WriteLog("电子钱包消费", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.code == "200" || response.code == "1")
            return Success(response.data);
        return Fail(response.message);
    }

    /// <summary>
    /// 电子钱包退款
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("refund")]
    public async Task<IActionResult> walletRefund([FromBody] walletRefundRequest request)
    {
        var parameter = new CardParameter();
        var dict = new Dictionary<string, object>();
        dict.Add("orgTradeNo", request.outTradeNo);
        dict.Add("outRefundNo", request.outRefundNo);
        dict.Add("tradeAmount", request.amount);
        dict.Add("shopCode", request.shopCode);
        parameter.url = $"{_cardOptions.service_url}/youy/card/trade/refund";
        parameter.body = dict;
        var response = await _cardService.getCardData(parameter);
        WriteLog("电子钱包退款", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.code == "200" || response.code == "1")
            return Success(response.data);
        return Fail(response.message);
    }

    /// <summary>
    /// 电子钱包交易查询
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("trade-query")]
    public async Task<IActionResult> walletTradeQuery([FromBody] walletTradeQueryRequest request)
    {
        if (request.tradeType == 1 && request.outTradeNo.IsNullOrEmpty())
            return Fail("交易单号不能为空！");
        if (request.tradeType == 2 && request.outRefundNo.IsNullOrEmpty())
            return Fail("退款单号不能为空！");
        var parameter = new CardParameter();
        var dict = new Dictionary<string, object>();
        if (request.tradeType == 1)
            dict.Add("outTradeNo", request.outTradeNo);
        if (request.tradeType == 2)
            dict.Add("outRefundNo", request.outRefundNo);
        dict.Add("tradeType", request.tradeType);
        parameter.url = $"{_cardOptions.service_url}/youy/card/trade/query";
        parameter.body = dict;
        var response = await _cardService.getCardData(parameter);
        WriteLog("电子钱查询", parameter.url, parameter.body.ToJson(), response.ToJson());
        if (response.code == "200" || response.code == "1")
            return Success(response.data);
        return Fail(response.message);
    }

    /// <summary>
    /// 手机开卡
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    //[HttpPost, Route("send")]
    [AllowAnonymous, NoSign, HttpPost, Route("send")]

    public async Task<IActionResult> WalletSendCard([FromBody] walletSendCardRequest request)
    {
        try
        {
            // 添加请求参数日志
            WriteLog("手机开卡-请求参数", "接收到的请求", "", request.ToJson());

            // 添加配置信息日志（不包含敏感信息）
            WriteLog("手机开卡-配置信息", "服务配置", "", new {
                service_url = _cardOptions.service_url,
                app_id = _cardOptions.app_id,
                has_sign_key = !string.IsNullOrEmpty(_cardOptions.sign_key)
            }.ToJson());

            var parameter = new CardParameter();
            var dict = new Dictionary<string, object>();
            dict.Add("serialNo", request.serialNo);
            dict.Add("outShopCode", request.outShopCode);
            dict.Add("phone", request.phone);

            // 确保 sendCardList 被正确序列化为数组
            var sendCardArray = request.sendCardList.Select(item => new Dictionary<string, object>
            {
                ["serialNumber"] = item.serialNumber,
                ["amount"] = item.amount,
                ["payAmount"] = item.payAmount
            }).ToArray();
            dict.Add("sendCardList", sendCardArray);

            parameter.url = $"{_cardOptions.service_url}/youy/card/send";
            parameter.body = dict;

            // 添加发送参数日志
            WriteLog("手机开卡-发送参数", parameter.url, parameter.body.ToJson(), "准备发送");

            var response = await _cardService.getCardData(parameter);
            WriteLog("手机开卡-响应结果", parameter.url, parameter.body.ToJson(), response.ToJson());

            if (response.code == "200" || response.code == "1")
                return Success(response.data);
            return Fail(response.message);
        }
        catch (Exception ex)
        {
            WriteLog("手机开卡-异常", "发生异常", ex.Message, ex.StackTrace);
            return Fail($"系统异常：{ex.Message}");
        }
    }


    /// <summary>
    /// 有赞会员二维码查询
    /// </summary>
    /// <param name="bar_code">条码/二维码</param>
    /// <returns></returns>
    private async Task<YouzanResult<object>> getMemberPhoneByCode(string bar_code)
    {
        var parameter = new YouzanParameter();
        parameter.url = $"/api/youzan.scrm.member.code.decode/1.0.0";
        var dict = new Dictionary<string, object>();
        dict.Add("keyword", bar_code);
        parameter.body = dict;
        var response = await _youzanService.getYouzanData(parameter);
        WriteLog("会员二维码查询", parameter.url, dict.ToJson(), response.ToJson());
        return response;

    }



    /// <summary>
    /// 日志记录
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="pay_way">支付网关</param>
    /// <param name="requestData">请求数据</param>
    /// <param name="rawData">原始响应</param>
    private void WriteLog(string title, string pay_way, string requestData, string resultData)
    {
        var content = new StringBuilder();
        content.AppendLine($"请求地址:{pay_way}");
        content.AppendLine($"请求参数:{requestData}");
        content.AppendLine($"返回结果:{resultData}");
        Log.Set<LogContent>(p => p.Class = GetType().FullName)
            .Set<LogContent>(p => p.Caption = title)
            .Set<LogContent>(p => p.Content = content)
           .Info();
    }

}
