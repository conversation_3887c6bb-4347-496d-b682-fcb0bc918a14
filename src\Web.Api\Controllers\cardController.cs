﻿using Api.Filter;
using BPM.Extras.Card.Options;
using BPM.Extras.Card.Request;
using BPM.Extras.Card.Services;
using Domain.Shared.Constant;
using Essensoft.Paylink.Alipay.Domain;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
/// <summary>
///  储值卡控制器
/// </summary>
[Route("api/card")]
[Authorize]
public class cardController : ApiControllerBase
{
    /// <summary>
    /// 订单应用服务
    /// </summary>
    private readonly ICardAppService _cardAppService;

    /// <summary>
    /// 储值卡第三方服务
    /// </summary>
    private readonly ICardService _cardService;

    /// <summary>
    ///储值卡配置.
    /// </summary>
    private readonly CardOptions _cardOptions;
    /// <summary>
    /// 初始化服务
    /// </summary>
    /// <param name="orderAppService">应用服务</param>
    public cardController(ICardAppService cardAppService, ICardService cardService, IOptions<CardOptions> cardOptions)
    {
        _cardAppService = cardAppService;
        _cardService = cardService;
        _cardOptions = cardOptions.Value ?? throw new ArgumentNullException(nameof(cardOptions));
    }

    /// <summary>
    /// 储值卡Token
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [AllowAnonymous, NoSignAttribute, HttpGet, Route("get_card_token")]
    public async Task<IActionResult> getCardToken()
    {
        var parameter = new CardParameter();
        var dict = new Dictionary<string, object>();
        dict.Add("outTradeNo", "S202411071111490102");
        dict.Add("tradeAmount", 210);
        dict.Add("shopCode", "1001");
        dict.Add("phone", "13800138000");
        parameter.url = _cardOptions.service_url + "/youy/card/trade/sale";
        parameter.body = dict;
        var result = await _cardService.getCardData(parameter);
        return Success(result);
    }


    /// <summary>
    /// 储值卡信息
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("card_info")]
    public async Task<IActionResult> getCardInfo([FromBody] cardQueryRequest request)
    {
        var data = await _cardAppService.getCardInfo(request);
        if (data.IsNull())
            return Fail(cardStatusConstant.error);
        data.CODE = request.card_code;
        return Success(data);
    }


    /// <summary>
    /// 储值卡充值规则列表
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("card_face_list")]
    public async Task<IActionResult> getCardFaceList([FromBody] cardFaceRequest request)
    {
        var data = await _cardAppService.getCardFaceList(request.shop_id);
        var jsonData = new
        {
            data = data, //数据
            total_page = 1,//总页数
            page = 1,//当前页
            records = data.Count,//总记录数
        };
        return Success(jsonData);
    }


    /// <summary>
    /// 储值信息核查信息
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("card_info_verify")]
    public async Task<IActionResult> getCardVerifyInfo([FromBody] cardvfRequest request)
    {
        var data = await _cardAppService.getCardInfo(request);
        if (!data.IsNull())
            return Fail("储值卡已激活，无法开卡");
        // 制卡信息
        var cardVf_info = await _cardAppService.getCardVFInfo(request);
        if (cardVf_info.IsNull())
            return Fail("储值卡号或校验码异常，无法开卡");
        //return Success("成功");
        var jsonData = "";
        return Success(jsonData);
    }


    /// <summary>
    /// 储值卡消费
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("card_txns")]
    public async Task<IActionResult> saveCardTxns([FromBody] cardTxnsRequest request)
    {
        var card = await _cardAppService.getCardInfo(new cardQueryRequest() { card_code = request.card_code });
        if (card.IsNull())
            return Fail(cardStatusConstant.error);//不存在
        if (!card.ISACTIVE)
            return Fail(cardStatusConstant.disabled);//禁用
        if (card.EXPDATE < DateTime.Now)
            return Fail(cardStatusConstant.stale_dated);//过期
        if (card.BALANCE < request.trade_fee.ToDecimal())
            return Fail(cardStatusConstant.not_balance);//余额不够
        await _cardAppService.saveCardTxns(request);
        var jsonData = new { balance = card.BALANCE - request.trade_fee.ToDecimal() };
        return Success(jsonData);
    }



    /// <summary>
    /// 储值卡充值
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost, Route("card_recharge")]
    //[NoSignAttribute]
    public async Task<IActionResult> cardRecharge([FromBody] cardRechargeRequest request)
    {
        Log.Info("储值卡充值请求参数--->" + request.ToJson());
        var query = new cardvfRequest() { card_code = request.card_code, vf_code = request.vf_code };
        var data = await _cardAppService.getCardInfo(query);
        if (!data.IsNull())
            return Fail("储值卡已激活，无法开卡");
        // 制卡信息
        var cardVf_info = await _cardAppService.getCardVFInfo(query);
        if (cardVf_info.IsNull())
            return Fail("储值卡号或校验码异常，无法开卡");
        await _cardAppService.cardRecharge(request, cardVf_info.VFCODE, cardVf_info.PASSWORD);
        var jsonData = new { balance = request.trade_fee };
        return Success(jsonData);
    }




}